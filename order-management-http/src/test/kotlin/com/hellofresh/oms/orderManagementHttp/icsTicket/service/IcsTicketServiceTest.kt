package com.hellofresh.oms.orderManagementHttp.icsTicket.service

import com.hellofresh.oms.orderManagementHttp.client.tapioca.config.WebClientConfiguration
import com.hellofresh.oms.orderManagementHttp.configuration.AbstractIntegrationTest
import com.hellofresh.oms.orderManagementHttp.icsTicket.IcsTicketRepository
import kotlin.test.assertEquals
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.web.reactive.function.client.WebClient

@SpringBootTest
@AutoConfigureMockMvc
@Tag("integration")
class IcsTicketServiceTest : AbstractIntegrationTest() {

    private lateinit var mockServer: MockWebServer

    private lateinit var icsTicketService: IcsTicketService

    @Autowired
    private lateinit var icsTicketRepository: IcsTicketRepository

    private val ticketsUrl = "/api/v2/tickets/"

    @BeforeEach
    fun setupMockServerAndClient() {
        MockWebServer().hostName
        mockServer = MockWebServer().also { it.start() }

        icsTicketService = IcsTicketService(
            webClient = WebClientConfiguration("").webClient(
                WebClient.builder(),
                ""
            ),
            icsTicketsUrl = mockServer.url(ticketsUrl).toString(),
            icsTicketRepository = icsTicketRepository
        )
    }

    @AfterEach
    fun shutdownMockServers() = mockServer.shutdown()

    @Test
    fun `should get tickets`() {
        mockServer.enqueue(
            MockResponse()
                .setBody(TICKETS_RESPONSE)
                .addHeader("Content-Type", "application/json")
        )
        icsTicketService.fetchTickets()

        val icsTickets = icsTicketService.getIcsTickets("po_number", "sku_code")
        assertEquals(1, icsTickets.size)
    }

    @Test
    fun `should fetch tickets`() {
        mockServer.enqueue(
            MockResponse()
                .setBody(TICKETS_RESPONSE)
                .addHeader("Content-Type", "application/json")
        )
        icsTicketService.fetchTickets()
        assertEquals(1, icsTicketRepository.count())
    }

    companion object {

        const val TICKETS_RESPONSE = """
            [
              {
                "id": 123456789,
                "po": "po_number",
                "subject": "5 - Potential Stoppage Today",
                "priority": 1,
                "status": 2,
                "updated_at": "2025-05-23T06:47:47Z",
                "custom_fields": {
                    "week": "2025-W22",
                    "site": "Irving - HF09",
                    "sku_code": "sku_code",
                    "ticket_link": "hellofresh/tickets/123456789",
                    "request_type": "5 - Production/Kitchen Shortage"
                }
              }
            ]
        """
    }
}
