package com.hellofresh.oms.orderManagementHttp.icsTicket.dto

import com.fasterxml.jackson.annotation.JsonProperty
import java.time.LocalDateTime

data class IcsTicketDto(
    val id: Int?,
    val po: String?,
    val subject: String?,
    val priority: Int?,
    val status: Int?,
    @JsonProperty("updated_at")
    val updatedAt: LocalDateTime?,
    @JsonProperty("custom_fields")
    val customFields: IcsTicketCustomFields,
)

data class IcsTicketCustomFields(
    val week: String,
    val site: String,
    @JsonProperty("sku_code")
    val skuCode: String?,
    @JsonProperty("ticket_link")
    val ticketLink: String?,
    @JsonProperty("request_type")
    val requestType: String?,
)
