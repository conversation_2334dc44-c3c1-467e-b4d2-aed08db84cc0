package com.hellofresh.oms.orderManagementHttp.icsTicket.service

import com.hellofresh.oms.model.icsTicket.IcsTicket
import com.hellofresh.oms.orderManagement.generated.api.model.IcsTicketResponseInner
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketDto

fun IcsTicketDto.toEntity() = IcsTicket(
    ticketId = id,
    market = "MARKET_US",
    week = customFields.week,
    bobCode = customFields.site,
    skuCode = customFields.skuCode,
    poNumber = po,
    subject = subject,
    ticketLink = customFields.ticketLink,
    priority = priority,
    requestType = customFields.requestType,
    status = status,
    updatedAt = updatedAt,
)

fun IcsTicket.toResponse() = IcsTicketResponseInner(
    week = week,
    bobCode = bobCode,
    subject = subject,
    poNumber = poNumber,
    skuCode = skuCode,
    ticketLink = ticketLink,
    requestType = requestType,
    ticketId = ticketId,
    status = status,
    updatedAt = updatedAt,
)
