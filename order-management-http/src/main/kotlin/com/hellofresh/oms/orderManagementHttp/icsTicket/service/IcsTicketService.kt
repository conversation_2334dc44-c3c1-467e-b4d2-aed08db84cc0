package com.hellofresh.oms.orderManagementHttp.icsTicket.service

import com.hellofresh.oms.model.icsTicket.IcsTicket
import com.hellofresh.oms.orderManagementHttp.icsTicket.IcsTicketRepository
import com.hellofresh.oms.orderManagementHttp.icsTicket.dto.IcsTicketDto
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.reactive.function.client.WebClient

@Service
class IcsTicketService(
    private val webClient: WebClient,
    private val icsTicketRepository: IcsTicketRepository,
    @Value("\${ics.tickets.url}") private val icsTicketsUrl: String,
) {

    fun getIcsTickets(poNumber: String, skuCode: String) =
        icsTicketRepository.findByPoNumberAndSkuCode(poNumber, skuCode)

    fun fetchTickets() {
        webClient.get()
            .uri(icsTicketsUrl)
            .retrieve()
            .bodyToFlux(IcsTicketDto::class.java)
            .collectList()
            .map { tickets -> filterNewTickets(tickets) }
            .doOnNext { tickets -> icsTicketRepository.saveAll(tickets) }
            .block()
    }

    private fun filterNewTickets(dtos: List<IcsTicketDto>): List<IcsTicket> {
        val incomingIds = dtos.mapNotNull { it.id }
        val existingIds = icsTicketRepository.findExistingTicketIds(incomingIds).toList()
        return dtos
            .filterNot { it.id in existingIds }
            .map { it.toEntity() }
    }
}
