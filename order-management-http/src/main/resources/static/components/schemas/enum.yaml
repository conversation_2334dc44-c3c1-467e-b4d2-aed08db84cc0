PurchaseOrderTypeEnum:
  type: string
  example: STANDARD
  enum:
    - STANDARD
    - EMERGENCY
    - PR<PERSON><PERSON><PERSON><PERSON>

PurchaseOrderSendStatusEnum:
  type: string
  example: SENT
  enum:
    - NOT_SENT # no send requests were created
    - PENDING # send request was created but not sent
    - SENT # send request was sent successfully
    - FAILED # send request failed

PurchaseOrderStatusEnum:
  type: string
  example: INITIATED
  enum:
    - INITIATED
    - APPROVED
    - REJECTED
    - DELETED

PoOverviewStatusEnum:
  type: string
  example: INITIATED
  enum:
    - INITIATED
    - APPROVED
    - REJECTED
    - DELETED

ShipMethodEnum:
  type: string
  example: CROSSDOCK
  enum:
    - CROSSDOCK
    - FREIGHT_ON_BOARD
    - VENDOR
    - OTHER

PoOverviewShipMethodEnum:
  type: string
  example: CROSSDOCK
  enum:
    - CROSSDOCK
    - FREIGHT_ON_BOARD
    - VENDOR
    - OTHER

PackagingTypeEnum:
  type: string
  example: UNIT
  enum:
    - UNIT
    - CASE
    - PALLET

UomEnum:
  type: string
  example: 'KG'
  enum:
    - UNIT
    - KG
    - L
    - LBS
    - GAL
    - OZ

ListPurchaseOrdersSortEnum:
  type: string
  enum:
    - "+created_at"
    - "-created_at"
    - "+delivery_window_start"
    - "-delivery_window_start"
    - "+po_number"
    - "-po_number"

ShippingMethodEnum:
  type: string
  enum:
    - "VENDOR"
    - "OTHER"
    - "FREIGHT_ON_BOARD"
    - "CROSSDOCK"

PurchaseOrderSendStatusFilterEnum:
  type: string
  enum:
    - SENT
    - NOT_SENT # PENDING and FAILED will be included in the response

PurchaseOrderGrnStatusFilterEnum:
  type: string
  enum:
    - OPENED
    - CLOSED

PurchaseOrderGrnStatusEnum:
  type: string
  enum:
    - OPENED
    - CLOSED
    - UNSPECIFIED

GrnDeliveryLineUnitOfMeasureEnum:
  type: string
  enum:
    - OTHER
    - KG
    - LBS
    - OZ
    - LITRE
    - GAL
    - UNIT
    - CASE
    - UNSPECIFIED
