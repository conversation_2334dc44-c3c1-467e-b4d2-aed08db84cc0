package com.hellofresh.oms.model.imt.exportReceipt

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.IdClass
import jakarta.persistence.Table
import java.io.Serializable
import java.time.LocalDateTime

@Entity
@Table(name = "export_receipt")
@IdClass(ExportReceiptId::class)
data class ExportReceipt(
    @Id
    @Column(name = "wh_id", nullable = false)
    val warehouseId: String,

    @Id
    @Column(name = "po_number", nullable = false)
    val poNumber: String,

    @Id
    @Column(name = "sku_code", nullable = false)
    val skuCode: String,

    @Column(name = "supplier_name", nullable = false)
    val supplierName: String,

    @Column(name = "cases_received", nullable = false)
    val casesReceived: Int,

    @Column(name = "quantity_received", nullable = false)
    val quantityReceived: Double,

    @Column(name = "sku_name", nullable = false)
    val skuName: String,

    @Column(name = "scm_week_raw", nullable = false)
    val scmWeekRaw: String,

    @Column(name = "status", nullable = false)
    val status: String,

    @Column(name = "receipt_time_est")
    val receiptTimeEst: LocalDateTime?,

    @Column(name = "unit", nullable = false)
    val unit: String,

    @Column(name = "market", nullable = false)
    val market: String,

    @Column(name = "supplier_code", nullable = false)
    val supplierCode: String
)

data class ExportReceiptId(
    val warehouseId: String = "",
    val poNumber: String = "",
    val skuCode: String = ""
) : Serializable {
    companion object {
        private const val serialVersionUID: Long = 1L
    }
}
