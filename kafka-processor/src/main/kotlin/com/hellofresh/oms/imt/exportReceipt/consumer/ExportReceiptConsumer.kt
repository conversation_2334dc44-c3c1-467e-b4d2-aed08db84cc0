package com.hellofresh.oms.imt.exportReceipt.consumer

import com.hellofresh.oms.imt.exportReceipt.service.ExportReceiptService
import java.util.UUID
import java.util.function.Consumer
import org.apache.avro.generic.GenericRecord
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.kafka.support.KafkaNull
import org.springframework.messaging.Message
import org.springframework.stereotype.Component

const val DELETE_MESSAGE = "d"

@Component
class ExportReceiptConsumer(
    private val exportReceiptService: ExportReceiptService
) {
    @Suppress("TooGenericExceptionCaught")
    @Bean
    fun processImtExportReceipt(): Consumer<Message<GenericRecord>> =
        Consumer { message ->
            try {
                if (message.payload is KafkaNull) {
                    logger.warn("Received KafkaNull payload, skipping.")
                    return@Consumer
                }
                if (message.payload["op"].toString() == DELETE_MESSAGE) {
                    logger.warn("Received Delete message from export receipt")
                    exportReceiptService.delete(
                        UUID.nameUUIDFromBytes(
                            generateUuidBytes(message.payload["before"] as GenericRecord)
                        )
                    )
                    return@Consumer
                }

                val record = message.payload["after"] as GenericRecord
                val value = record.toExportReceipt()
                logger.warn("processing export receipt: {}", value)
                exportReceiptService.save(value)
            } catch (e: Exception) {
                logger.error(
                    "Couldn't process export receipt unexpected error{$e}",
                )
                throw e
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(this::class.java)
    }
}
