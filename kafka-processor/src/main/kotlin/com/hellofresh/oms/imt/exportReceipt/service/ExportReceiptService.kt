package com.hellofresh.oms.imt.exportReceipt.service

import com.hellofresh.oms.imt.exportReceipt.repository.ExportReceiptRepository
import com.hellofresh.oms.model.imt.exportReceipt.ExportReceipt
import io.micrometer.core.annotation.Timed
import java.util.UUID
import org.springframework.stereotype.Service

@Service
class ExportReceiptService(
    private val exportReceiptRepository: ExportReceiptRepository,
) {
    @Timed
    fun delete(id: UUID) {
        exportReceiptRepository.deleteById(id)
    }

    @Timed
    fun save(exportReceipt: ExportReceipt) {
        exportReceiptRepository.save(exportReceipt)
    }
}
