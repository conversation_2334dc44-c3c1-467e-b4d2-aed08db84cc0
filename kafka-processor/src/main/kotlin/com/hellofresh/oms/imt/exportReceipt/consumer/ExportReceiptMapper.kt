package com.hellofresh.oms.imt.exportReceipt.consumer

import com.hellofresh.oms.model.YearWeek
import com.hellofresh.oms.model.imt.exportReceipt.ExportReceipt
import java.util.UUID
import org.apache.avro.generic.GenericRecord

const val WEEK_MAX_LENGTH = 6
const val HF_YEAR_OFFSET = 4
const val HF_WEEK_OFFSET = 2
const val PO_NUMBER = "po_number"

fun GenericRecord.toExportReceipt(): ExportReceipt =
    ExportReceipt(
        id = UUID.nameUUIDFromBytes(
            generateUuidBytes(this),
        ),
        warehouseId = this["wh_id"].toString(),
        supplierName = this["supplier_name"].toString(),
        poNumber = this[PO_NUMBER].toString().split('_').first(),
        poReference = this[PO_NUMBER].toString(),
        casesReceived = this["cases_received"] as? Int ?: 0,
        quantityReceived = this["quantity_received"] as? Double ?: 0.0,
        skuCode = this["sku_code"].toString(),
        skuName = this["sku_name"].toString(),
        yearWeek = calculateYearWeek(this["scm_week_raw"].toString()),
        status = this["status"].toString(),
        unit = this["unit"].toString(),
        market = this["market"].toString(),
        supplierCode = this["supplier_code"].toString(),
    )

fun generateUuidBytes(record: GenericRecord): ByteArray =
    record[PO_NUMBER].toString().toByteArray() +
        record["sku_code"].toString().toByteArray() +
        record["wh_id"].toString().toByteArray() +
        record["scm_week_raw"].toString().toByteArray() +
        record["cases_received"].toString().toByteArray() +
        record["quantity_received"].toString().toByteArray() +
        record["supplier_code"].toString().toByteArray() +
        record["market"].toString().toByteArray()

fun calculateYearWeek(imtWeek: String): YearWeek {
    require(imtWeek.length == WEEK_MAX_LENGTH) { "imtWeek must be 6 characters long" }
    val year = imtWeek.take(HF_YEAR_OFFSET)
    val week = imtWeek.takeLast(HF_WEEK_OFFSET)
    return YearWeek(year, week)
}
