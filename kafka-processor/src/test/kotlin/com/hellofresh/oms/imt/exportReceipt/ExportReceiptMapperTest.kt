package com.hellofresh.oms.imt.exportReceipt

import com.hellofresh.oms.imt.exportReceipt.Fixture.getGenericRecordExportReceipt
import com.hellofresh.oms.imt.exportReceipt.consumer.toExportReceipt
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test

class ExportReceiptMapperTest {

    @Test
    fun `should map GenericRecord to ExportReceipt entity`() {
        // given
        val warehouseId = "HF08"
        val supplierName = "Igors Seasonal Harvest"
        val poNumber = "2513IM432778"
        val poReference = "2513IM432778_O1"
        val casesReceived = 0
        val quantityReceived = 0.0
        val skuCode = "PHF-10-10378-4"
        val skuName = "Spinach, Baby - 5 Ounce (oz)"
        val scmWeekRaw = "202519"
        val status = "Rejected"
        val unit = "ea"
        val market = "US"
        val supplierCode = "40416"

        val genericRecord = getGenericRecordExportReceipt(
            warehouseId = warehouseId,
            supplierName = supplierName,
            poNumber = poReference,
            casesReceived = casesReceived,
            quantityReceived = quantityReceived,
            skuCode = skuCode,
            skuName = skuName,
            scmWeekRaw = scmWeekRaw,
            status = status,
            unit = unit,
            market = market,
            supplierCode = supplierCode
        )

        // when
        val entity = genericRecord.toExportReceipt()

        // then
        assertEquals(warehouseId, entity.warehouseId)
        assertEquals(supplierName, entity.supplierName)
        assertEquals(poReference, entity.poNumber)
        assertEquals(casesReceived, entity.casesReceived)
        assertEquals(quantityReceived, entity.quantityReceived)
        assertEquals(skuCode, entity.skuCode)
        assertEquals(skuName, entity.skuName)
        assertEquals(scmWeekRaw, entity.scmWeekRaw)
        assertEquals(status, entity.status)
        assertNull(entity.receiptTimeEst)
        assertEquals(unit, entity.unit)
        assertEquals(market, entity.market)
        assertEquals(supplierCode, entity.supplierCode)
    }

    @Test
    fun `should map GenericRecord with non-zero values to ExportReceipt entity`() {
        // given
        val warehouseId = "HF08"
        val supplierName = "Igors Seasonal Harvest"
        val poNumber = "2513IM432778"
        val poReference = "2513IM432778_O1"
        val casesReceived = 5
        val quantityReceived = 10.5
        val skuCode = "PHF-10-10378-4"
        val skuName = "Spinach, Baby - 5 Ounce (oz)"
        val scmWeekRaw = "202519"
        val status = "Received"
        val unit = "ea"
        val market = "US"
        val supplierCode = "40416"

        val genericRecord = getGenericRecordExportReceipt(
            warehouseId = warehouseId,
            supplierName = supplierName,
            poNumber = poReference,
            casesReceived = casesReceived,
            quantityReceived = quantityReceived,
            skuCode = skuCode,
            skuName = skuName,
            scmWeekRaw = scmWeekRaw,
            status = status,
            unit = unit,
            market = market,
            supplierCode = supplierCode
        )

        // when
        val entity = genericRecord.toExportReceipt()

        // then
        assertEquals(warehouseId, entity.warehouseId)
        assertEquals(supplierName, entity.supplierName)
        assertEquals(poReference, entity.poNumber)
        assertEquals(casesReceived, entity.casesReceived)
        assertEquals(quantityReceived, entity.quantityReceived)
        assertEquals(skuCode, entity.skuCode)
        assertEquals(skuName, entity.skuName)
        assertEquals(scmWeekRaw, entity.scmWeekRaw)
        assertEquals(status, entity.status)
        assertNull(entity.receiptTimeEst)
        assertEquals(unit, entity.unit)
        assertEquals(market, entity.market)
        assertEquals(supplierCode, entity.supplierCode)
    }
}
