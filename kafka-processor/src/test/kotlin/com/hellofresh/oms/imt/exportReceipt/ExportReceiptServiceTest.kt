package com.hellofresh.oms.imt.exportReceipt

import com.hellofresh.oms.imt.exportReceipt.Fixture.getExportReceiptEntity
import com.hellofresh.oms.imt.exportReceipt.repository.ExportReceiptRepository
import com.hellofresh.oms.imt.exportReceipt.service.ExportReceiptService
import java.util.UUID
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever

@ExtendWith(MockitoExtension::class)
class ExportReceiptServiceTest {
    @Mock
    lateinit var exportReceiptRepositoryMock: ExportReceiptRepository

    @InjectMocks
    lateinit var service: ExportReceiptService

    @Test
    fun `should save new export receipt`() {
        // given
        val exportReceiptEntity = getExportReceiptEntity()
        whenever(
            exportReceiptRepositoryMock.save(exportReceiptEntity)
        ).thenReturn(exportReceiptEntity)

        // when
        service.save(exportReceiptEntity)

        // then
        verify(exportReceiptRepositoryMock).save(exportReceiptEntity)
    }

    @Test
    fun `should delete export receipt`() {

        val exportReceiptEntity = getExportReceiptEntity()
        whenever(
            exportReceiptRepositoryMock.findById(exportReceiptEntity.id)
        ).thenReturn(Optional.of(exportReceiptEntity))

        service.delete(exportReceiptEntity.id)
        verify(exportReceiptRepositoryMock).deleteById(exportReceiptEntity.id)
    }
}
