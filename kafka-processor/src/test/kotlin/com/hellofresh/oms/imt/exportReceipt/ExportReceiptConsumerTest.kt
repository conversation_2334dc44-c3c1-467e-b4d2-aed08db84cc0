package com.hellofresh.oms.imt.exportReceipt

import com.hellofresh.oms.imt.exportReceipt.Fixture.getGenericRecordExportReceipt
import com.hellofresh.oms.imt.exportReceipt.Fixture.getGenericRecordExportReceiptDelete
import com.hellofresh.oms.imt.exportReceipt.consumer.ExportReceiptConsumer
import com.hellofresh.oms.imt.exportReceipt.service.ExportReceiptService
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.assertThrows
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doThrow
import org.springframework.kafka.support.KafkaNull
import org.springframework.messaging.Message
import org.springframework.messaging.MessageHeaders

@ExtendWith(MockitoExtension::class)
class ExportReceiptConsumerTest {
    @Mock
    private lateinit var exportReceiptServiceMock: ExportReceiptService

    @Test
    fun `should call ExportReceipt service method when consuming message`() {
        // given
        val subject = ExportReceiptConsumer(exportReceiptServiceMock)

        // when
        subject.processImtExportReceipt().accept(
            object : Message<GenericRecord> {
                override fun getPayload(): GenericRecord = getGenericRecordExportReceipt()

                override fun getHeaders(): MessageHeaders = MessageHeaders(
                    mapOf<String, String>("kafka_receivedMessageKey" to UUID.randomUUID().toString()),
                )
            },
        )

        // then
        verify(exportReceiptServiceMock).save(any())
    }
}
